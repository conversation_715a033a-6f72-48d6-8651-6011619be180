import { Ri<PERSON>ore2Fill } from "@remixicon/react";
import { CustomDropdown, DataGridTable } from "components";
import {
  KnowledgeBaseToolbar,
  PermissionGroup,
} from "features/KnowledgeDatabase/components";
import { FILE_TYPE_INFO, IMAGE_PATH } from "globals";
import { useDebounce } from "hooks";
import CreateOrganisation from "pages/OrganisationManager/CreateOrganisation";
import UseOrgManagerConfig from "pages/OrganisationManager/useOrgManagerConfig";
import { useState } from "react";
import { Image } from "react-bootstrap";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { getFileExtension, getFormattedDate } from "utils";
import { useGetKnowledgeBase } from "../api";
import { OWNER_TYPES } from "../globals";
import useDeleteFileOrFolder from "../hooks/useDeleteFileOrFolder";
import useHideOrShowDocs from "../hooks/useHideOrShowDocs";
import { KNOWLEDGE_BASE_ROUTE_PATH } from "../routePath";
import "./styles.scss";

export interface FilterInterface {
  search?: string | undefined;
  owner_type?: string | undefined;
  sort?: string;
}

export default function KnowledgeBase() {
  const [paginationConfig, setPaginationConfig] = useState<any>({
    page: 1,
    limit: 25,
  });
  const [filters, setFilters] = useState<FilterInterface>({
    search: undefined,
    owner_type: OWNER_TYPES.ORGANIZATION,
    sort: "asc",
  });

  const { parent_id }: { parent_id?: string } = useParams();

  const debouncedQry = useDebounce(filters.search, 500);
  const [searchParams] = useSearchParams();
  const owner_type = searchParams.get("owner_type") || OWNER_TYPES.ORGANIZATION;
  const {
    data: { files = [], count: totalCount, breadcrumb: breadcrumbs = [] } = {},
    isLoading,
  } = useGetKnowledgeBase({
    ...paginationConfig,
    owner_type,
    search: debouncedQry,
    parent_id,
  });

  const navigate = useNavigate();
  const { onClickDelete } = useDeleteFileOrFolder();
  const { onClickHideOrShow } = useHideOrShowDocs();
  const { organization } = UseOrgManagerConfig();

  const isOrganizationExist =
    organization?.id && owner_type === OWNER_TYPES.ORGANIZATION;
  const isActionsEnabled =
    isOrganizationExist || owner_type === OWNER_TYPES.USER;

  const columns = [
    {
      field: "name",
      headerName: "File Name",
      renderCell: (row: any) => {
        const extension = getFileExtension(row?.name);
        const fileInfo = FILE_TYPE_INFO[extension]
          ? FILE_TYPE_INFO[extension]
          : {
              icon:
                row?.type === "file"
                  ? IMAGE_PATH.colorInfoDoc
                  : IMAGE_PATH.folderIcon,
            };

        return (
          <div className="table-user-card d-flex justify-content-start align-items-center">
            <Image
              src={fileInfo.icon}
              alt={`${fileInfo.type || "File"} Icon`}
              className="d-block table-user-image object-fit-cover"
              style={{ width: "30px", height: "30px" }}
            />

            <div className="table-user-card-data">
              <h3 className="mb-0 name">{row?.name}</h3>
            </div>
          </div>
        );
      },
    },
    {
      field: "created_at",
      headerName: "Created Date",
      renderCell: (row: any) => (
        <p
          className="mb-0 d-flex justify-content-start align-items-center"
          style={{ gap: "10px" }}
        >
          {getFormattedDate(row?.created_at) ?? "NA"}
        </p>
      ),
    },
    owner_type === OWNER_TYPES.ORGANIZATION && {
      field: "role",
      headerName: "Permission Group",
      renderCell: (row: any) => (
        <PermissionGroup row={row} disabled={parent_id} />
      ),
    },
    {
      field: "actions",
      headerName: "Actions",
      renderCell: (row: any) => {
        const onSelect = (value: string) => {
          if (value === "delete") {
            onClickDelete({ type: row?.type, itemId: row?.id });
          }

          if (value === "hide" || value === "show") {
            onClickHideOrShow({ itemInfo: row });
          }
        };
        return (
          <div className="d-flex gap-2" onClick={(e) => e.stopPropagation()} key={row?.id}>
            <CustomDropdown
              title={<RiMore2Fill size={18} />}
              items={[{ label: "Delete", value: "delete" }].concat(
                parent_id
                  ? []
                  : [
                      row?.access_control?.visible
                        ? { label: "Hide", value: "hide" }
                        : { label: "Show", value: "show" },
                    ]
              )}
              className="table-actions"
              onSelect={onSelect}
              preserveTitle
            />
          </div>
        );
      },
    },
  ].filter(Boolean);

  return (
    <div className="knowledge-base-wrapper">
      <KnowledgeBaseToolbar
        setFilters={setFilters}
        filters={filters}
        breadcrumbs={breadcrumbs}
        owner_type={owner_type}
        isActionsEnabled={isActionsEnabled}
      />
      {isActionsEnabled ? (
        <DataGridTable
          columns={columns}
          rows={files}
          paginationProps={{
            paginationConfig,
            setPaginationConfig,
            totalCount,
          }}
          sortDirection={filters.sort}
          onSort={() => {
            setFilters({
              ...filters,
              sort: filters.sort === "asc" ? "desc" : "asc",
            });
          }}
          onRowClick={(row: any) => {
            if (row?.type === "folder") {
              navigate(
                `${KNOWLEDGE_BASE_ROUTE_PATH.KNOWLEDGE_BASE}/${row?.id}?owner_type=${owner_type}`
              );
            }
          }}
          loading={isLoading}
        />
      ) : (
        <CreateOrganisation />
      )}
    </div>
  );
}
