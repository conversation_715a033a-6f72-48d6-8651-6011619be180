import {
  Ri<PERSON>rrowLeftSLine,
  RiArrowRightSLine,
  RiCloseLine,
  RiSparkling2Line,
} from "@remixicon/react";
import { DefaultProfile } from "components/Common";
import { KNOWLEDGE_BASE_ROUTE_PATH } from "features/KnowledgeDatabase/routePath";
import GeneratedReportsHistoryBox from "features/Reports/components/GeneratedReportsHistoryBox";
import useCheckReportSection from "features/Reports/components/ReportBuilder/hooks/useCheckReportSection";
import ReportsHistoryBox from "features/Reports/components/ReportsHistoryBox";
import { SETTINGS } from "globals";
import { useFeatureFlags } from "hooks";
import { FeatureFlagName } from "hooks/useFeatureFlags";
import { useState } from "react";
import { Button, Image, Offcanvas } from "react-bootstrap";
import { Link, useNavigate } from "react-router-dom";
import { ROUTE_PATH } from "routes";
import useUserStore from "stores/user";
import { generateNickName } from "utils";
import HistoryBox from "../HistoryBox";
import "./styles.scss";

interface HistoryOffcanvasProps {
  show: boolean;
  handleClose: () => void;
}

type ViewType = "menu" | "history" | "report" | "knowledge-base" | "contact";

interface SubMenuItem {
  key: string;
  label: string;
  route: string;
}

interface MenuItem {
  key: ViewType;
  label: string;
  route?: string | null;
  children?: SubMenuItem[];
  featureFlag?: FeatureFlagName;
}

const HistoryOffcanvas: React.FC<HistoryOffcanvasProps> = ({
  show,
  handleClose,
}) => {
  const MENU_CONFIG: { menu: MenuItem[] } = {
    menu: [
      {
        key: "history",
        label: "Chat History",
        route: null,
      },
      {
        key: "report",
        label: "Report",
        route: null,
        featureFlag: "REPORTS",
      },
      {
        key: "knowledge-base",
        label: "Knowledge Base",
        children: [
          {
            key: "kb-docs",
            label: "Knowledge Base",
            route: KNOWLEDGE_BASE_ROUTE_PATH.KNOWLEDGE_BASE,
          },
          {
            key: "faq-prompts",
            label: "FAQ Prompts",
            route: KNOWLEDGE_BASE_ROUTE_PATH.FAQ_PROMPTS,
          },
        ],
        featureFlag: "KNOWLEDGE_BASE",
      },
      {
        key: "contact",
        label: "Contact Us",
        route: ROUTE_PATH.CONTACT_US,
      },
    ],
  };

  const navigate = useNavigate();
  const user = useUserStore((state) => state.userInfo.user);
  const defaultView: ViewType = "menu";

  const [activeView, setActiveView] = useState<ViewType>(defaultView);
  const [submenuItems, setSubmenuItems] = useState<SubMenuItem[]>([]);

  const { isGenerateSection } = useCheckReportSection();
  const { isFeatureEnabled } = useFeatureFlags();

  const handleBackToMenu = () => {
    setActiveView("menu");
    setSubmenuItems([]);
  };

  const renderMainMenu = () => (
    <div className="flex-grow-1 overflow-auto">
      <div className="p-3 d-flex flex-column gap-3">
        {MENU_CONFIG.menu.map((item) => {
          if (item.featureFlag && !isFeatureEnabled(item.featureFlag))
            return null;

          return (
            <button
              key={item.key}
              className="mobile-navigation-btn d-flex justify-content-between align-items-center"
              onClick={() => {
                if (item.children) {
                  setSubmenuItems(item.children);
                  setActiveView(item.key);
                } else if (item.route) {
                  handleClose();
                  navigate(item.route);
                } else {
                  setActiveView(item.key);
                }
              }}
            >
              {item.label}
              <RiArrowRightSLine />
            </button>
          );
        })}
      </div>
    </div>
  );

  const renderSubmenu = (items: SubMenuItem[]) => (
    <div className="flex-grow-1 overflow-auto">
      <div className="p-3 d-flex flex-column gap-3">
        {items.map((subItem) => (
          <button
            key={subItem.key}
            className="mobile-navigation-btn d-flex justify-content-between align-items-center"
            onClick={() => {
              handleClose();
              navigate(subItem.route);
            }}
          >
            {subItem.label}
            <RiArrowRightSLine />
          </button>
        ))}
      </div>
    </div>
  );

  const renderView = () => {
    switch (activeView) {
      case "menu":
        return renderMainMenu();
      case "history":
        return <HistoryBox onCloseCanvas={handleClose} />;
      case "report":
        return isGenerateSection ? (
          <GeneratedReportsHistoryBox onCloseCanvas={handleClose} />
        ) : (
          <ReportsHistoryBox onCloseCanvas={handleClose} />
        );
      case "knowledge-base":
        return renderSubmenu(submenuItems);
      default:
        return renderMainMenu();
    }
  };

  return (
    <Offcanvas
      show={show}
      onHide={handleClose}
      className="chat-history-offcanvas"
    >
      <Offcanvas.Header className="chat-history-offcanvas-header flex-wrap pb-0">
        <Offcanvas.Title className="d-flex justify-content-between w-100">
          <p className="mb-0 page-title fw-bold">{SETTINGS.APP_NAME}</p>
          <div className="d-flex gap-2">
            {activeView !== "menu" && (
              <Button
                variant=""
                className="bg-blue px-1 py-1"
                onClick={handleBackToMenu}
              >
                <RiArrowLeftSLine color="white" />
              </Button>
            )}
            <Button
              variant=""
              className="bg-blue px-1 py-1"
              onClick={handleClose}
            >
              <RiCloseLine color="white" />
            </Button>
          </div>
        </Offcanvas.Title>
        <hr className="mt-0 d-block w-100 order-3" />
      </Offcanvas.Header>

      <Offcanvas.Body className="chat-history-offcanvas-body p-0 d-flex flex-column">
        {renderView()}
        <div className="history-footer position-sticky bottom-0 d-flex flex-column bg-light">
          <Link
            to={ROUTE_PATH.SUBSCRIPTIONS}
            onClick={handleClose}
            className="history-footer-profile d-flex font-primary text-decoration-none"
            style={{ gap: "10px" }}
          >
            <RiSparkling2Line
              color="#0d3149"
              size={"35px"}
              className="rounded-circle"
            />
            <div>
              <h6 className="fw-bold mb-1">Upgrade plan</h6>
              <small className="mb-0">upgrade your plan today</small>
            </div>
          </Link>

          <Link
            to={ROUTE_PATH.SETTINGS}
            onClick={handleClose}
            className="history-footer-plans d-flex font-primary text-decoration-none fw-bold align-items-center text-truncate"
          >
            {user?.profile_photo ? (
              <Image
                src={user?.profile_photo}
                className="object-fit-cover profile-data-img rounded-circle bg-brown"
                alt="user"
              />
            ) : (
              <DefaultProfile
                text={generateNickName(user?.full_name)}
                className="small"
              />
            )}
            <span className="text-truncate">{user?.full_name}</span>
          </Link>
        </div>
      </Offcanvas.Body>
    </Offcanvas>
  );
};

export default HistoryOffcanvas;
