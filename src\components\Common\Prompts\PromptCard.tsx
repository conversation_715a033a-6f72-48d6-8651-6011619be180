import { Ri<PERSON><PERSON>cilFill } from "@remixicon/react";
import { useSendMessageMutation } from "api";
import { AddPromptModal } from "components/Common";
import { DEFAULT_PROMPT_ICONS } from "globals";
import { useMemo, useState } from "react";
import { Button, Card, Image } from "react-bootstrap";
import { useNavigate, useParams } from "react-router-dom";
import { handleSendTextMessage } from "utils";

interface PromptCardProps {
  allStoredPrompts: any[];
  promptData: any;
  promptConfig?: {
    isPromptEnabled: boolean;
    isPromptEditable: boolean;
    rightIcon?: string;
    placeholderIcon?: string;
    rightText?: string;
  };
  onSavePrompt?: (prompt_data: any) => void;
  onResetPrompt?: (data: any) => void;
  onLocalReset?: any;
  customCardClassName?: any;
}

const PromptCard = ({
  allStoredPrompts,
  promptData,
  promptConfig = { isPromptEnabled: true, isPromptEditable: true },
  onSavePrompt,
  onResetPrompt,
  onLocalReset,
  customCardClassName = {},
}: PromptCardProps) => {
  const { title, icon, prompt, prompt_id, template } = promptData ?? {};
  const navigate = useNavigate();
  const { id: chat_id } = useParams();
  const {
    isPromptEnabled,
    isPromptEditable,
    rightIcon,
    placeholderIcon,
    rightText,
  } = promptConfig;

  const { mutateAsync: sendMessage } = useSendMessageMutation();

  const isStoredPrompt = useMemo(
    () => allStoredPrompts.some((item: any) => item.prompt_id === prompt_id),
    [allStoredPrompts, prompt_id]
  );

  const CardIcon = DEFAULT_PROMPT_ICONS[icon] || DEFAULT_PROMPT_ICONS.email;

  const onClickPrompt = async () => {
    if (!isPromptEnabled) return;

    await handleSendTextMessage({
      inputValue: title,
      chat_id,
      navigate,
      sendMessage,
      prompt: template ? `${prompt}\n\n${template}` : prompt,
      prompt_id: prompt_id || undefined,
    });
  };

  const [showAddPromptModal, setShowAddPromptModal] = useState(false);

  return (
    <>
      <Card className="m-0 p-0 position-relative">
        <Card.Body onClick={onClickPrompt} className="cursor-pointer">
          <CardIcon size={30} className="prompt-icon" />
          <p className={`mb-0 ${customCardClassName?.title || ""}`}>{title}</p>
        </Card.Body>

        {isPromptEditable && (
          <>
            {placeholderIcon && (
              <div
                className="prompt-action-button text-center d-flex justify-content-center align-items-center rounded-circle text-decoration-none position-absolute border-0"
                style={{
                  right: "45px",
                }}
              >
                <Image src={placeholderIcon} width={25} height={25} />
              </div>
            )}
            <Button
              variant="link"
              className="prompt-action-button text-center d-flex justify-content-center align-items-center rounded-circle text-decoration-none position-absolute"
              onClick={() => setShowAddPromptModal(true)}
            >
              <RiPencilFill color="#0d3149" size={22} />
            </Button>
          </>
        )}
        {(rightIcon || rightText) && !isPromptEditable && (
          <div className="prompt-action-button text-center d-flex justify-content-center align-items-center rounded-circle text-decoration-none position-absolute border-0 px-3">
            {rightText ? (
              <span className="text-uppercase fw-bold font-brown">{rightText}</span>
            ) : (
              <Image src={rightIcon} width={25} height={25} />
            )}
          </div>
        )}
      </Card>

      <AddPromptModal
        show={showAddPromptModal}
        onClose={() => setShowAddPromptModal(false)}
        promptData={promptData}
        isStoredPrompt={isStoredPrompt}
        onOpen={() => setShowAddPromptModal(true)}
        onSavePrompt={onSavePrompt}
        onResetPrompt={onResetPrompt}
        onLocalReset={onLocalReset}
      />
    </>
  );
};

export default PromptCard;
